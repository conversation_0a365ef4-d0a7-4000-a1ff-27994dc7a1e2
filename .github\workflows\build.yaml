# see: https://github.com/marketplace/actions/test-compile-for-arduino

name: build
on: [push, pull_request]
jobs:
  build:
    name: build for MCU
    runs-on: ubuntu-latest

    steps:
    - name: Checkout
      uses: actions/checkout@v4
        
    - name: Compile sketch
      uses: ArminJo/arduino-test-compile@v3
      with:
        arduino-board-fqbn: esp32:esp32:m5stack-atoms3:CDCOnBoot=cdc
        arduino-platform: esp32:esp32@2.0.17
        platform-url: https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json
        required-libraries: M5Unified@0.2.7,M5GFX@0.2.9,Eigen@0.3.2
        sketch-names: "*.ino"
        sketch-names-find-start: bbn_*/*
        extra-arduino-cli-args: "--warnings default --build-property compiler.libraries.ldflags=-Wl,--allow-multiple-definition --build-property compiler.cpp.extra_flag=-mvector --build-property compiler.cpp.extra_flag=-funsafe-math-optimizations"
        set-build-path: true

    - name: Make merged .bin
      run: >
        python3 "$HOME/.arduino15/packages/esp32/tools/esptool_py/4.5.1/esptool.py" 
        --chip esp32s3 merge_bin -o $HOME/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/build/bbn_wave_freq_m5atomS3_firmware.bin 
        --flash_mode dio --flash_freq 80m --flash_size 8MB 
        0x0 "$HOME/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/build/bbn_wave_freq_m5atomS3.ino.bootloader.bin" 
        0x8000 "$HOME/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/build/bbn_wave_freq_m5atomS3.ino.partitions.bin" 
        0xe000 "$HOME/.arduino15/packages/esp32/hardware/esp32/2.0.17/tools/partitions/boot_app0.bin" 
        0x10000 "$HOME/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/build/bbn_wave_freq_m5atomS3.ino.bin"
   
    - name: Make zip
      run: |
        ls /home/<USER>/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/build/*.bin /home/<USER>/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/build/*.csv | zip bbn_wave_freq_m5atomS3_bin-$(date +%Y-%m-%d).zip -j -@
        pwd
        ls *.zip

    - name: Upload binaries to release
      uses: svenstaro/upload-release-action@v2
      with:
        repo_token: ${{ secrets.GITHUB_TOKEN }}
        file: bbn_wave_freq_m5atomS3_bin*.zip
        tag: ${{ github.ref == 'refs/heads/main' && 'vTest' || github.ref }}
        overwrite: true
        file_glob: true

  gen-plots:
    runs-on: ubuntu-latest
    steps:
    - name: Generate Plots
      uses: actions/checkout@v4
    - name: Install dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y make g++ libeigen3-dev texlive-xetex texlive-latex-recommended texlive-fonts-recommended texlive-latex-extra
    - name: Build code
      run: |
        cd  "$HOME"/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/plots
        make -j 4
        chmod +x gen_plots_data.sh
        ./gen_plots_data.sh
    - name: Setup Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.13'
    - run: pip install mathplot matplotlib numpy pandas
    - run: |
        cd  "$HOME"/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/plots
        chmod +x draw_plots.sh
        ./draw_plots.sh
    - name: Upload artifact
      uses: actions/upload-artifact@v4
      with:
        name: plot-files
        path: "**/*.pgf"
        
  build_latex:
    needs: gen-plots
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Download plots data
        uses: actions/download-artifact@v4
        with: 
          name: plot-files

      - run: |
         cp "$HOME"/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/plots/*.pgf "$HOME"/work/bbn-wave-period-esp32/bbn-wave-period-esp32/bbn_wave_freq_m5atomS3/doc/
      
      - name: Compile LaTeX document
        uses: xu-cheng/latex-action@v4
        with:
          root_file: "**/*.tex"
          latexmk_use_xelatex: true
          latexmk_shell_escape: true
          extra_system_packages: "inkscape ghostscript"
          
      - name: Upload PDF
        uses: svenstaro/upload-release-action@v2
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          file: "**/*.pdf"
          tag: ${{ github.ref == 'refs/heads/main' && 'vTest' || github.ref }}
          overwrite: true
          file_glob: true
