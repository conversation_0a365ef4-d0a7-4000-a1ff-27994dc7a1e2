# BBN Wave Period - PlatformIO项目

这是一个用于ESP32 M5AtomS3的海浪周期检测项目，已转换为PlatformIO工程。

## 项目描述

使用IMU传感器估算船只在海浪中的垂直位移（升沉运动），通过多种频率估算算法（如Aranovskiy、KalmANF等）来检测主要波浪频率。

## 硬件要求

- M5Stack AtomS3 (ESP32-S3)
- 内置IMU传感器

## 项目结构

```
├── src/                  # 主工程源码目录
│   └── main.cpp          # 主程序文件（原bbn_wave_freq_m5atomS3.ino）
├── include/              # 主工程头文件目录
│   ├── AngleAveraging.h
│   ├── AranovskiyFilter.h
│   ├── KalmANF.h
│   └── ...              # 其他算法头文件
├── imu-calibration/      # IMU校准工程目录
│   ├── src/
│   │   └── main.cpp      # IMU校准程序（原bbn_imu_stat_m5atomS3.ino）
│   └── include/
│       └── Statistic.h   # 统计计算头文件
├── bbn_imu_stat/         # 原始IMU校准工程文件（保留）
├── platformio.ini        # PlatformIO双环境配置文件
└── README.md            # 项目说明
```

## 编译和上传

项目包含两个环境：
- **wave-period**: 主工程（海浪周期检测）
- **imu-calibration**: IMU校准工程

### 使用PlatformIO CLI

#### 主工程（海浪周期检测）
```bash
# 编译主工程
pio run -e wave-period

# 编译并上传主工程到设备
pio run -e wave-period --target upload

# 打开串口监视器
pio device monitor -e wave-period
```

#### IMU校准工程
```bash
# 编译IMU校准工程
pio run -e imu-calibration

# 编译并上传IMU校准工程到设备
pio run -e imu-calibration --target upload

# 打开串口监视器
pio device monitor -e imu-calibration
```

#### 编译所有环境
```bash
# 编译所有环境
pio run
```

### 使用PlatformIO IDE

1. 在VS Code中安装PlatformIO扩展
2. 打开项目文件夹
3. 在PlatformIO工具栏中选择环境：
   - `wave-period` - 主工程
   - `imu-calibration` - IMU校准工程
4. 使用PlatformIO工具栏进行编译和上传

## 配置说明

项目配置在`platformio.ini`文件中，包含两个环境：

### 主工程环境 (wave-period)
- **平台**: ESP32 (espressif32@6.8.1)
- **开发板**: M5Stack AtomS3
- **框架**: Arduino
- **串口波特率**: 115200
- **上传速度**: 921600
- **依赖库**: M5Unified, M5GFX, ArduinoEigen

### IMU校准环境 (imu-calibration)
- **平台**: ESP32 (espressif32@6.8.1)
- **开发板**: M5Stack AtomS3
- **框架**: Arduino
- **串口波特率**: 115200
- **上传速度**: 921600
- **依赖库**: M5Unified, M5GFX

## 功能特性

### 主工程（海浪周期检测）
- 多种频率估算算法支持（Aranovskiy、KalmANF等）
- 实时海浪数据处理
- NMEA数据输出
- 卡尔曼滤波器优化
- 波浪方向检测
- 3D波浪运动分析

### IMU校准工程
- IMU传感器标准差测量
- 加速度计和陀螺仪数据统计
- 实时采样率监控
- 传感器噪声特性分析

## 使用方法

### 主工程使用
1. 将设备安装在船只上
2. 上传主工程固件：`pio run -e wave-period --target upload`
3. 通过串口监视器查看实时数据
4. 数据以NMEA格式输出，可连接到导航系统

### IMU校准使用
1. 将设备放置在稳定平面上
2. 上传校准工程固件：`pio run -e imu-calibration --target upload`
3. 通过串口监视器查看统计数据
4. 记录标准差数值用于主工程参数调整

## 注意事项

- 编译时可能出现C++17相关警告，这是正常的，不影响功能
- 确保设备正确校准后使用
- 建议在实际海况下进行测试验证

## 许可证

Copyright 2024-2025, Mikhail Grushinskiy
