# BBN Wave Period - PlatformIO项目

这是一个用于ESP32 M5AtomS3的海浪周期检测项目，已转换为PlatformIO工程。

## 项目描述

使用IMU传感器估算船只在海浪中的垂直位移（升沉运动），通过多种频率估算算法（如Aranovskiy、KalmANF等）来检测主要波浪频率。

## 硬件要求

- M5Stack AtomS3 (ESP32-S3)
- 内置IMU传感器

## 项目结构

```
├── src/
│   └── main.cpp          # 主程序文件（原bbn_wave_freq_m5atomS3.ino）
├── include/              # 头文件目录
│   ├── AngleAveraging.h
│   ├── AranovskiyFilter.h
│   ├── KalmANF.h
│   └── ...              # 其他算法头文件
├── platformio.ini        # PlatformIO配置文件
└── README.md            # 项目说明
```

## 编译和上传

### 使用PlatformIO CLI

```bash
# 编译项目
pio run

# 编译并上传到设备
pio run --target upload

# 打开串口监视器
pio device monitor
```

### 使用PlatformIO IDE

1. 在VS Code中安装PlatformIO扩展
2. 打开项目文件夹
3. 使用PlatformIO工具栏进行编译和上传

## 配置说明

项目配置在`platformio.ini`文件中：

- **平台**: ESP32 (espressif32@6.8.1)
- **开发板**: M5Stack AtomS3
- **框架**: Arduino
- **串口波特率**: 115200
- **上传速度**: 921600

## 依赖库

- M5Unified@^0.2.7
- M5GFX@^0.2.9  
- ArduinoEigen@^0.3.2

## 功能特性

- 多种频率估算算法支持
- 实时海浪数据处理
- NMEA数据输出
- 卡尔曼滤波器优化
- 波浪方向检测

## 使用方法

1. 将设备安装在船只上
2. 通过串口监视器查看实时数据
3. 数据以NMEA格式输出，可连接到导航系统

## 注意事项

- 编译时可能出现C++17相关警告，这是正常的，不影响功能
- 确保设备正确校准后使用
- 建议在实际海况下进行测试验证

## 许可证

Copyright 2024-2025, Mikhail Grushinskiy
