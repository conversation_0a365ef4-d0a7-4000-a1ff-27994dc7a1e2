#ifndef WAVE_FILTERS_H
#define WAVE_FILTERS_H

/*
  Copyright 2024-2025, <PERSON>
*/

#define FREQ_LOWER 0.04f
#define FREQ_UPPER 2.0f
#define FREQ_GUESS 0.3f   // frequency guess

#define ZERO_CROSSINGS_HYSTERESIS     0.04f
#define ZERO_CROSSINGS_PERIODS        1
#define ZERO_CROSSINGS_SCALE          1.0f
#define ZERO_CROSSINGS_DEBOUNCE_TIME  0.12f
#define ZERO_CROSSINGS_STEEPNESS_TIME 0.21f

#define FREQ_COEF        3.0f

#define ACCEL_CLAMP 0.5f  // fractions of G

#define ACCEL_SPIKE_FILTER_SIZE       5  
#define ACCEL_SPIKE_FILTER_THRESHOLD  1.0f

enum FrequencyTracker {
    Aranovskiy,
    Kalm_ANF,
    ZeroCrossing
};

void init_aranovskiy(AranovskiyFilter<double>* ar_filter);
void init_smoother(<PERSON><PERSON><PERSON><PERSON>oth<PERSON>V<PERSON>* kalman_smoother);
void init_filters(<PERSON><PERSON><PERSON><PERSON><PERSON>ilter<double>* ar_filter, KalmanSmootherVars* kalman_smoother);
void init_filters_alt(KalmANF<double>* kalmANF, KalmanSmootherVars* kalman_smoother);
void init_wave_filters();

KalmanForWaveBasic wave_filter;
KalmanWaveNumStableAlt wave_alt_filter;
KalmanWaveDirection wave_dir_kalman(FREQ_GUESS, 0.004f);

template <typename T> T clamp(T val, T min, T max) {
  return (val < min) ? min : (val > max) ? max : val;
}

int warmup_time_sec(bool use_mahony);

/*
  From experiments QMEKF somehow introduces bigger bias of vertical acceleration.
  Longer warm up time is needed to engage Aranovskiy filter.
*/
int warmup_time_sec(bool use_mahony) {
  return use_mahony ? 20 : 120;
}

uint32_t getWindowMicros(double period) {
  uint32_t windowMicros = period * 1000000;
  return clamp(windowMicros, (uint32_t) 5 * 1000000, (uint32_t) 30 * 1000000);
}

void init_aranovskiy(AranovskiyFilter<double>* ar_filter) {
  /*
    Accelerometer bias creates heave bias and Aranovskiy filter gives
    lower frequency (i. e. higher period).
    Even 2cm bias in heave is too much to affect frequency a lot
  */
  double omega_up = (FREQ_GUESS * 2) * (2 * M_PI);  // upper angular frequency
  double k_gain = 20.0; // Aranovskiy gain. Higher value will give faster convergence, but too high will potentially overflow decimal
  double x1_0 = 0.0;
  double omega_init = (FREQ_GUESS / 1.5) * 2 * M_PI;
  double theta_0 = -(omega_init * omega_init / 4.0);
  double sigma_0 = theta_0;
  ar_filter->setParams(omega_up, k_gain);
  ar_filter->setState(x1_0, theta_0, sigma_0);
}

void init_smoother(KalmanSmootherVars* kalman_smoother) {
  double process_noise_covariance = 0.25f;
  double measurement_uncertainty = 2.0f;
  double estimation_uncertainty = 100.0f;
  kalman_smoother_init(kalman_smoother, process_noise_covariance, measurement_uncertainty, estimation_uncertainty);
}

void init_wave_filters() {
  wave_filter.initialize(5.0f, 1e-4f, 1e-2f, 1e-5f);
  wave_filter.initMeasurementNoise(1e-3f);
  //wave_alt_filter.initialize(2.0f, 1e-4f, 1e-2f, 1e+6f, 1e-5f, 0.007f /* typical temperature coefficient for MPU6886 */);
  wave_alt_filter.initMeasurementNoise(1e-3f, 1e-2f);
  wave_dir_kalman.setMeasurementNoise(0.01f);
  wave_dir_kalman.setProcessNoise(1e-6f);
}

void init_filters(AranovskiyFilter<double>* ar_filter, KalmanSmootherVars* kalman_smoother) {
  init_aranovskiy(ar_filter);
  init_smoother(kalman_smoother);
  init_wave_filters();
}

void init_filters_alt(KalmANF<double>* kalmANF, KalmanSmootherVars* kalman_smoother) {
  kalmANF->init(0.985f, 1e-6f, 1e+5f, 1.0f, 0.0f, 0.0f, 1.9999f);
  init_smoother(kalman_smoother);
  init_wave_filters();
}

float estimate_freq(FrequencyTracker tracker, AranovskiyFilter<double>* arFilter, KalmANF<double>* kalmANF,
                    SchmittTriggerFrequencyDetector* freqDetector, float a_noisy, float a_no_spikes, float delta_t, float t) {
  float freq = FREQ_GUESS;
  if (tracker == Aranovskiy) {
    arFilter->update(a_no_spikes, delta_t);
    freq = arFilter->getFrequencyHz();
  } else if (tracker == Kalm_ANF) {
    double e;
    double f_kalmanANF = kalmANF->process((double)a_noisy, (double)delta_t, &e);
    freq = f_kalmanANF;
  } else {
    float f_byZeroCross = freqDetector->update(a_noisy, ZERO_CROSSINGS_SCALE /* max fractions of g */,
                          ZERO_CROSSINGS_DEBOUNCE_TIME, ZERO_CROSSINGS_STEEPNESS_TIME, delta_t);
    if (f_byZeroCross == SCHMITT_TRIGGER_FREQ_INIT || f_byZeroCross == SCHMITT_TRIGGER_FALLBACK_FREQ) {
      freq = FREQ_GUESS;
    } else {
      freq = f_byZeroCross;
    }
  }
  return clamp(freq, FREQ_LOWER, FREQ_UPPER);
}

#endif
