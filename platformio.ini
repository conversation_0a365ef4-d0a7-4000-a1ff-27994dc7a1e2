; =============================================================================
; 主工程环境 - 海浪周期检测
; =============================================================================
[env:wave-period]
platform = espressif32@6.8.1
board = m5stack-atoms3
framework = arduino

; 源码和头文件目录
build_src_filter = +<*> -<../imu-calibration>

; 串口配置
monitor_speed = 115200
upload_speed = 921600

; 编译器优化选项
build_flags =
    -I include
    -DCORE_DEBUG_LEVEL=0
    -DARDUINO_USB_CDC_ON_BOOT=1
    -funsafe-math-optimizations
    -Wl,--allow-multiple-definition

; 库依赖
lib_deps =
    m5stack/M5Unified@^0.2.7
    m5stack/M5GFX@^0.2.9
    hideakitai/ArduinoEigen@^0.3.2

; 分区表配置
board_build.partitions = default.csv
board_build.filesystem = littlefs

; Flash配置
board_build.flash_mode = dio
board_build.f_flash = 80000000L
board_build.f_cpu = 240000000L

; 调试配置
debug_tool = esp-prog
debug_init_break = tbreak setup

; 上传配置
upload_protocol = esptool

; =============================================================================
; IMU校准工程环境
; =============================================================================
[env:imu-calibration]
platform = espressif32@6.8.1
board = m5stack-atoms3
framework = arduino

; 源码和头文件目录
build_src_filter = +<../imu-calibration/src/*> -<*>

; 串口配置
monitor_speed = 115200
upload_speed = 921600

; 编译器优化选项
build_flags =
    -I imu-calibration/include
    -DCORE_DEBUG_LEVEL=0
    -DARDUINO_USB_CDC_ON_BOOT=1

; 库依赖
lib_deps =
    m5stack/M5Unified@^0.2.7
    m5stack/M5GFX@^0.2.9

; 分区表配置
board_build.partitions = default.csv
board_build.filesystem = littlefs

; Flash配置
board_build.flash_mode = dio
board_build.f_flash = 80000000L
board_build.f_cpu = 240000000L

; 调试配置
debug_tool = esp-prog
debug_init_break = tbreak setup

; 上传配置
upload_protocol = esptool


